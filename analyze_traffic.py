#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import sys
import os

try:
    from scapy.all import *
    from scapy.layers.inet import IP, TCP, UDP, ICMP
    from scapy.layers.l2 import Ether
    from scapy.layers.http import HTTPRequest, HTTPResponse
except ImportError:
    print("Scapy not installed. Installing...")
    os.system("pip install scapy")
    from scapy.all import *
    from scapy.layers.inet import IP, TCP, UDP, ICMP
    from scapy.layers.l2 import Ether

def analyze_pcap(filename):
    """分析pcap文件"""
    print(f"正在分析文件: {filename}")
    
    try:
        # 读取pcap文件
        packets = rdpcap(filename)
        print(f"总共读取到 {len(packets)} 个数据包")
        
        # 统计信息
        protocols = {}
        src_ips = {}
        dst_ips = {}
        ports = {}
        
        # 存储可疑数据
        suspicious_data = []
        
        for i, pkt in enumerate(packets):
            # 协议统计
            if pkt.haslayer(IP):
                proto = pkt[IP].proto
                if proto in protocols:
                    protocols[proto] += 1
                else:
                    protocols[proto] = 1
                
                # IP地址统计
                src_ip = pkt[IP].src
                dst_ip = pkt[IP].dst
                
                if src_ip in src_ips:
                    src_ips[src_ip] += 1
                else:
                    src_ips[src_ip] = 1
                    
                if dst_ip in dst_ips:
                    dst_ips[dst_ip] += 1
                else:
                    dst_ips[dst_ip] = 1
            
            # TCP端口统计
            if pkt.haslayer(TCP):
                sport = pkt[TCP].sport
                dport = pkt[TCP].dport
                
                if sport in ports:
                    ports[sport] += 1
                else:
                    ports[sport] = 1
                    
                if dport in ports:
                    ports[dport] += 1
                else:
                    ports[dport] = 1
            
            # 查找可疑数据包
            if pkt.haslayer(Raw):
                payload = pkt[Raw].load
                try:
                    payload_str = payload.decode('utf-8', errors='ignore')
                    # 查找flag相关内容
                    if 'flag' in payload_str.lower() or 'ctf' in payload_str.lower():
                        suspicious_data.append({
                            'packet_num': i,
                            'payload': payload_str,
                            'raw_payload': payload
                        })
                except:
                    # 尝试查找二进制中的flag模式
                    if b'flag' in payload.lower() or b'ctf' in payload.lower():
                        suspicious_data.append({
                            'packet_num': i,
                            'payload': payload.hex(),
                            'raw_payload': payload
                        })
        
        # 打印统计信息
        print("\n=== 协议统计 ===")
        for proto, count in sorted(protocols.items(), key=lambda x: x[1], reverse=True):
            proto_name = {1: 'ICMP', 6: 'TCP', 17: 'UDP'}.get(proto, f'Protocol-{proto}')
            print(f"{proto_name}: {count}")
        
        print("\n=== 源IP统计 (Top 10) ===")
        for ip, count in sorted(src_ips.items(), key=lambda x: x[1], reverse=True)[:10]:
            print(f"{ip}: {count}")
        
        print("\n=== 目标IP统计 (Top 10) ===")
        for ip, count in sorted(dst_ips.items(), key=lambda x: x[1], reverse=True)[:10]:
            print(f"{ip}: {count}")
        
        print("\n=== 端口统计 (Top 10) ===")
        for port, count in sorted(ports.items(), key=lambda x: x[1], reverse=True)[:10]:
            print(f"{port}: {count}")
        
        # 打印可疑数据
        if suspicious_data:
            print("\n=== 发现可疑数据 ===")
            for data in suspicious_data:
                print(f"数据包 #{data['packet_num']}:")
                print(f"载荷: {data['payload']}")
                print("-" * 50)
        
        # 详细分析前几个数据包
        print("\n=== 前10个数据包详细信息 ===")
        for i in range(min(10, len(packets))):
            pkt = packets[i]
            print(f"\n数据包 #{i}:")
            print(pkt.summary())
            if pkt.haslayer(Raw):
                try:
                    payload = pkt[Raw].load.decode('utf-8', errors='ignore')
                    if payload.strip():
                        print(f"载荷: {payload[:200]}...")
                except:
                    print(f"二进制载荷: {pkt[Raw].load.hex()[:100]}...")
        
        return packets, suspicious_data
        
    except Exception as e:
        print(f"分析文件时出错: {e}")
        return None, None

def search_for_flags(packets):
    """专门搜索flag"""
    print("\n=== 搜索Flag ===")
    
    flag_patterns = [
        b'flag{',
        b'FLAG{',
        b'ctf{',
        b'CTF{',
        b'flag:',
        b'FLAG:',
    ]
    
    found_flags = []
    
    for i, pkt in enumerate(packets):
        if pkt.haslayer(Raw):
            payload = pkt[Raw].load
            
            for pattern in flag_patterns:
                if pattern in payload:
                    # 尝试提取完整的flag
                    try:
                        payload_str = payload.decode('utf-8', errors='ignore')
                        print(f"在数据包 #{i} 中发现可能的flag:")
                        print(f"完整载荷: {payload_str}")
                        found_flags.append(payload_str)
                    except:
                        print(f"在数据包 #{i} 中发现可能的flag (二进制):")
                        print(f"十六进制: {payload.hex()}")
                        found_flags.append(payload.hex())
    
    return found_flags

if __name__ == "__main__":
    filename = "scan.pcapng"
    
    if not os.path.exists(filename):
        print(f"文件 {filename} 不存在")
        sys.exit(1)
    
    packets, suspicious_data = analyze_pcap(filename)
    
    if packets:
        flags = search_for_flags(packets)
        
        if flags:
            print(f"\n=== 找到 {len(flags)} 个可能的flag ===")
            for i, flag in enumerate(flags):
                print(f"Flag {i+1}: {flag}")
        else:
            print("\n未找到明显的flag，需要进一步分析...")
            
            # 尝试其他分析方法
            print("\n=== 尝试其他分析方法 ===")
            
            # 查看所有TCP流
            tcp_streams = {}
            for pkt in packets:
                if pkt.haslayer(TCP) and pkt.haslayer(IP):
                    stream_key = f"{pkt[IP].src}:{pkt[TCP].sport} -> {pkt[IP].dst}:{pkt[TCP].dport}"
                    if stream_key not in tcp_streams:
                        tcp_streams[stream_key] = []
                    if pkt.haslayer(Raw):
                        tcp_streams[stream_key].append(pkt[Raw].load)
            
            print(f"发现 {len(tcp_streams)} 个TCP流")
            for stream, payloads in tcp_streams.items():
                if payloads:
                    combined_payload = b''.join(payloads)
                    try:
                        decoded = combined_payload.decode('utf-8', errors='ignore')
                        if 'flag' in decoded.lower() or len(decoded) > 50:
                            print(f"\nTCP流 {stream}:")
                            print(f"内容: {decoded[:500]}...")
                    except:
                        if len(combined_payload) > 20:
                            print(f"\nTCP流 {stream} (二进制):")
                            print(f"十六进制: {combined_payload.hex()[:200]}...")
